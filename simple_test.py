#!/usr/bin/env python3
"""
Simple test to check main focus detection
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_focus_detection():
    """Test the main focus detection for _calculate_position_quantity."""
    
    print("Testing main focus detection...")
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test with the same query that showed truncation
        test_query = "how does the position quantity calculator function work?"
        
        print(f"Testing query: '{test_query}'")
        
        # Create IR context request
        request = IRContextRequest(
            user_query=test_query,
            task_description=f"Analyze and provide context for: {test_query}",
            task_type="general_analysis",
            focus_entities=["position", "quantity", "calculator", "function"],
            max_tokens=2000,
            llm_friendly=True,
            max_entities=3,  # Limit to just 3 entities to see debug output clearly
            include_ir_slices=True,
            include_code_context=True
        )
        
        print("Processing IR context request...")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        if "error" in result:
            print(f"Error: {result['error']}")
            return False
        
        print("Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_focus_detection()
