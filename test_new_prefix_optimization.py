#!/usr/bin/env python3
"""
Test the "new/" prefix optimization for efficient follow-up questions
"""

import sys
import os
import time
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_new_prefix_optimization():
    """Test the new/ prefix optimization for IR context generation."""
    
    print("🧪 Testing 'new/' Prefix Optimization")
    print("=" * 60)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        from aider.context_request import ContextRequestHand<PERSON>, IRContextRequest
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test sequence: 
        # 1. First question (should generate context)
        # 2. Follow-up without "new/" (should be efficient - no IR generation)
        # 3. Follow-up with "new/" (should generate fresh context)
        test_scenarios = [
            {
                "query": "How does the compute_next_boundary function work?",
                "description": "First question",
                "should_generate_ir": True,
                "expected_behavior": "Generate fresh IR context"
            },
            {
                "query": "What parameters does it take?",
                "description": "Follow-up without 'new/' prefix",
                "should_generate_ir": False,
                "expected_behavior": "Use existing context (efficient)"
            },
            {
                "query": "new/how does the close_position_based_on_conditions function work?",
                "description": "Follow-up with 'new/' prefix",
                "should_generate_ir": True,
                "expected_behavior": "Generate fresh IR context"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📝 Test {i}: {scenario['description']}")
            print(f"   Query: '{scenario['query']}'")
            print(f"   Expected: {scenario['expected_behavior']}")
            
            start_time = time.time()
            
            # Create IR context request
            request = IRContextRequest(
                user_query=scenario['query'],
                task_description=f"Analyze and provide context for: {scenario['query']}",
                task_type="general_analysis",
                focus_entities=[],
                max_tokens=2000,
                llm_friendly=True,
                max_entities=8,
                include_ir_slices=True,
                include_code_context=True
            )
            
            print(f"🔄 Processing request {i}...")
            
            # Process the request
            result = handler.process_ir_context_request(request)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if "error" in result:
                print(f"❌ Test {i} failed: {result['error']}")
                results.append({
                    "test": i,
                    "passed": False,
                    "reason": f"Error: {result['error']}",
                    "time": processing_time
                })
                continue
            
            print(f"✅ Test {i} processed in {processing_time:.2f}s")
            
            # Check if LLM-friendly package was generated
            if "llm_friendly_package" in result:
                package = result["llm_friendly_package"]
                print(f"📦 Package generated: {len(package)} characters")
                
                # Verify the behavior matches expectations
                if scenario['should_generate_ir']:
                    # Should have generated fresh context
                    if processing_time > 1.0:  # IR generation takes time
                        print(f"✅ Test {i}: Fresh IR context generated (took {processing_time:.2f}s)")
                        
                        # Check for the specific function
                        if i == 1:
                            # First query: compute_next_boundary
                            if "compute_next_boundary" in package.lower():
                                print(f"✅ Test {i}: compute_next_boundary found in package")
                                results.append({"test": i, "passed": True, "time": processing_time})
                            else:
                                print(f"❌ Test {i}: compute_next_boundary NOT found")
                                results.append({"test": i, "passed": False, "reason": "Function not found", "time": processing_time})
                        elif i == 3:
                            # Third query: close_position_based_on_conditions (with new/ prefix)
                            if "close_position_based_on_conditions" in package.lower():
                                print(f"✅ Test {i}: close_position_based_on_conditions found in package")
                                results.append({"test": i, "passed": True, "time": processing_time})
                            else:
                                print(f"❌ Test {i}: close_position_based_on_conditions NOT found")
                                results.append({"test": i, "passed": False, "reason": "Function not found", "time": processing_time})
                    else:
                        print(f"⚠️  Test {i}: Expected IR generation but was too fast ({processing_time:.2f}s)")
                        results.append({"test": i, "passed": False, "reason": "Too fast for IR generation", "time": processing_time})
                else:
                    # Should have used existing context (efficient)
                    if processing_time < 0.5:  # Should be very fast
                        print(f"✅ Test {i}: Efficient follow-up (took {processing_time:.2f}s)")
                        results.append({"test": i, "passed": True, "time": processing_time})
                    else:
                        print(f"⚠️  Test {i}: Expected efficiency but took {processing_time:.2f}s")
                        results.append({"test": i, "passed": False, "reason": "Too slow for follow-up", "time": processing_time})
            else:
                print(f"❌ Test {i}: No LLM-friendly package generated")
                results.append({"test": i, "passed": False, "reason": "No package generated", "time": processing_time})
        
        # Evaluate results
        print(f"\n📊 Optimization Test Results:")
        print(f"=" * 50)
        
        for result in results:
            status = "✅ PASS" if result['passed'] else "❌ FAIL"
            reason = f" ({result['reason']})" if not result['passed'] else ""
            print(f"   Test {result['test']}: {status} - {result['time']:.2f}s{reason}")
        
        passed_tests = sum(1 for r in results if r['passed'])
        total_tests = len(results)
        
        print(f"\n🎯 Summary: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print(f"\n🎉 OPTIMIZATION SUCCESS!")
            print(f"   ✅ First questions generate fresh context")
            print(f"   ✅ Follow-ups use existing context (efficient)")
            print(f"   ✅ 'new/' prefix forces fresh context generation")
            print(f"\n💡 Usage Guide:")
            print(f"   • Normal question: 'How does X work?' → Fresh context")
            print(f"   • Follow-up: 'What about Y?' → Uses existing context")
            print(f"   • New context: 'new/How does Z work?' → Fresh context")
        else:
            print(f"\n❌ OPTIMIZATION INCOMPLETE")
            print(f"   Some tests failed - check implementation")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the new/ prefix optimization test."""
    print("🚀 Testing 'new/' Prefix Optimization for IR Context")
    print("=" * 70)
    
    success = test_new_prefix_optimization()
    
    if success:
        print("\n🎉 OPTIMIZATION TEST PASSED!")
        print("The 'new/' prefix system is working correctly for efficient follow-up questions.")
    else:
        print("\n❌ OPTIMIZATION TEST FAILED!")
        print("The 'new/' prefix system needs additional work.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
