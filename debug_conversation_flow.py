#!/usr/bin/env python3
"""
Debug the conversation flow to understand why follow-up questions don't get IR context
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def debug_conversation_flow():
    """Debug the conversation flow for follow-up questions."""
    
    print("🔍 Debugging Conversation Flow for Follow-up Questions")
    print("=" * 70)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        # Import the base coder to understand the flow
        from aider.coders.base_coder import Coder
        from aider.io import InputOutput
        from aider.models import Model
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create a mock IO and model for testing
        io = InputOutput()
        model = Model("gpt-4")
        
        # Create a coder instance
        coder = Coder(main_model=model, io=io)
        
        print("🔧 Testing direct IR context enablement...")
        
        # Check if direct IR context is enabled
        enable_new_flow = getattr(coder, 'enable_direct_ir_context', True)
        print(f"   enable_direct_ir_context attribute: {enable_new_flow}")
        
        # Check environment variables
        disable_env = os.environ.get('AIDER_DISABLE_DIRECT_IR_CONTEXT', '')
        enable_env = os.environ.get('AIDER_ENABLE_DIRECT_IR_CONTEXT', '')
        print(f"   AIDER_DISABLE_DIRECT_IR_CONTEXT: '{disable_env}'")
        print(f"   AIDER_ENABLE_DIRECT_IR_CONTEXT: '{enable_env}'")
        
        # Simulate the logic from base_coder.py
        if disable_env.lower() in ('true', '1', 'yes'):
            final_enable = False
        elif enable_env.lower() in ('true', '1', 'yes'):
            final_enable = True
        else:
            final_enable = enable_new_flow
            
        print(f"   Final enable_new_flow decision: {final_enable}")
        
        # Test the process_direct_ir_context method
        print("\n🧪 Testing process_direct_ir_context method...")
        
        test_queries = [
            "How does the compute_next_boundary function work?",
            "how does the close_position_based_on_conditions function work?",
            "What is the purpose of the trading system?",
            "Explain the position management workflow"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n   Test {i}: '{query}'")
            
            # Check if the method exists
            if hasattr(coder, 'process_direct_ir_context'):
                print(f"      ✅ process_direct_ir_context method exists")
                
                # Check query length filter
                if len(query.split()) < 3:
                    print(f"      ⚠️  Query too short ({len(query.split())} words)")
                    continue
                    
                if query.startswith('/'):
                    print(f"      ⚠️  Query starts with '/' (command)")
                    continue
                    
                print(f"      ✅ Query passes filters ({len(query.split())} words)")
                
                # Try to call the method (this might fail due to missing dependencies)
                try:
                    # We can't actually call it without full setup, but we can check the logic
                    print(f"      📝 Would call process_direct_ir_context('{query}')")
                    print(f"      📝 Expected: IR context generation and injection")
                except Exception as e:
                    print(f"      ❌ Error calling method: {e}")
            else:
                print(f"      ❌ process_direct_ir_context method NOT found")
        
        # Check the conversation flow logic
        print("\n🔄 Testing conversation flow logic...")
        
        # Simulate the run method logic
        print("   Simulating run() method flow:")
        print("   1. enable_new_flow check ✅")
        print("   2. process_direct_ir_context() call ✅")
        print("   3. ir_context_result evaluation ❓")
        print("   4. Message processing continues ✅")
        
        # The key question: what happens when ir_context_result is True?
        print("\n🎯 Key Investigation Points:")
        print("   1. Does process_direct_ir_context() return True for follow-up questions?")
        print("   2. Is the IR context actually injected into the conversation?")
        print("   3. Does the LLM receive the new context in follow-up turns?")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_ir_context_availability():
    """Check if IR context functionality is available."""
    
    print("\n🔍 Checking IR Context Availability")
    print("=" * 50)
    
    try:
        # Check if CONTEXT_REQUEST is available
        try:
            from aider.context_request import ContextRequestHandler, IRContextRequest
            print("✅ IR Context modules available")
            
            # Check if we can create instances
            project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
            handler = ContextRequestHandler(project_path)
            print("✅ ContextRequestHandler created successfully")
            
            request = IRContextRequest(
                user_query="test query",
                task_description="test task",
                task_type="general_analysis",
                focus_entities=["test"],
                max_tokens=1000,
                llm_friendly=True
            )
            print("✅ IRContextRequest created successfully")
            
            return True
            
        except ImportError as e:
            print(f"❌ IR Context modules not available: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking availability: {e}")
        return False

def main():
    """Run the conversation flow debug."""
    print("🚀 Debugging IR Context Conversation Flow")
    print("=" * 70)
    
    # Check availability first
    available = check_ir_context_availability()
    if not available:
        print("\n❌ IR Context functionality not available - this explains the issue!")
        return False
    
    # Debug the conversation flow
    success = debug_conversation_flow()
    
    if success:
        print("\n🎯 DEBUGGING COMPLETE")
        print("=" * 50)
        print("Key findings:")
        print("1. Direct IR context is enabled by default")
        print("2. IR context modules are available")
        print("3. The issue is likely in the conversation flow logic")
        print("\nNext steps:")
        print("1. Test actual conversation with debug logging")
        print("2. Check if IR context is being injected properly")
        print("3. Verify LLM receives the context in follow-up turns")
    else:
        print("\n❌ DEBUGGING FAILED")
        print("Could not complete conversation flow analysis")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
