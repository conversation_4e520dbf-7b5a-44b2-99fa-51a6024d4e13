#!/usr/bin/env python3
"""
Debug script to find compute_next_boundary function in IR data
"""

import json
import sys
import os

def find_compute_next_boundary():
    """Find compute_next_boundary function in the live_backtest_dashboard IR data."""
    
    ir_file = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____\ir_data_20250530_220424.json"
    
    if not os.path.exists(ir_file):
        print(f"❌ IR file not found: {ir_file}")
        return
    
    print(f"🔍 Analyzing IR data: {ir_file}")
    
    try:
        with open(ir_file, 'r') as f:
            data = json.load(f)
        
        print(f"📊 Total modules: {len(data.get('modules', []))}")
        
        # Find compute_next_boundary function
        found_functions = []
        
        for module in data.get('modules', []):
            module_name = module.get('module_name', 'unknown')
            
            for func in module.get('functions', []):
                func_name = func.get('name', '')
                if 'compute_next_boundary' in func_name.lower():
                    found_functions.append({
                        'module': module_name,
                        'function': func_name,
                        'details': func
                    })
                    print(f"✅ Found: {module_name}.{func_name}")
        
        if found_functions:
            print(f"\n🎯 Found {len(found_functions)} matching functions:")
            for i, func_info in enumerate(found_functions, 1):
                print(f"\n{i}. {func_info['module']}.{func_info['function']}")
                details = func_info['details']
                print(f"   Type: {details.get('type', 'unknown')}")
                print(f"   Criticality: {details.get('criticality', 'unknown')}")
                print(f"   Change Risk: {details.get('change_risk', 'unknown')}")
                print(f"   Side Effects: {details.get('side_effects', [])}")
                print(f"   Calls: {len(details.get('calls', []))} functions")
                print(f"   Used by: {len(details.get('used_by', []))} functions")
        else:
            print("❌ No compute_next_boundary function found!")
            
            # Search for similar function names
            print("\n🔍 Searching for similar function names...")
            similar_functions = []
            
            for module in data.get('modules', []):
                module_name = module.get('module_name', 'unknown')
                
                for func in module.get('functions', []):
                    func_name = func.get('name', '')
                    if any(word in func_name.lower() for word in ['compute', 'boundary', 'next']):
                        similar_functions.append(f"{module_name}.{func_name}")
            
            if similar_functions:
                print(f"📝 Found {len(similar_functions)} similar functions:")
                for func in similar_functions[:10]:  # Show first 10
                    print(f"   - {func}")
            else:
                print("❌ No similar functions found")
    
    except Exception as e:
        print(f"❌ Error analyzing IR data: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    find_compute_next_boundary()
