#!/usr/bin/env python3
"""
Test the enhanced matching logic for compute_next_boundary
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_enhanced_matching():
    """Test the enhanced matching logic directly."""
    
    print("🧪 Testing Enhanced Code Matching Logic")
    print("=" * 60)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test with the specific query that was failing
        test_query = "How does the compute_next_boundary function work?"
        
        print(f"📝 Testing query: {test_query}")
        
        # Create IR context request with debug enabled
        request = IRContextRequest(
            user_query=test_query,
            task_description=f"Analyze and provide context for: {test_query}",
            task_type="general_analysis",
            focus_entities=["compute_next_boundary", "function", "work"],
            max_tokens=2000,
            llm_friendly=True,
            max_entities=8,
            include_ir_slices=True,
            include_code_context=True
        )
        
        print("🔄 Processing IR context request with enhanced matching...")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        if "error" in result:
            print(f"❌ IR context request failed: {result['error']}")
            return False
        
        print("✅ IR context request processed successfully!")
        
        # Check the IR slices to see if compute_next_boundary was found
        ir_slices = result.get("ir_slices", [])
        compute_boundary_found = False
        
        print(f"\n📊 IR Slices Analysis ({len(ir_slices)} entities):")
        for i, ir_slice in enumerate(ir_slices, 1):
            entity_name = ir_slice.get('entity_name', 'unknown')
            relevance_score = ir_slice.get('relevance_score', 0)
            priority = ir_slice.get('priority', 'unknown')
            
            print(f"   {i}. {entity_name} (score: {relevance_score:.2f}, priority: {priority})")
            
            if 'compute_next_boundary' in entity_name.lower():
                compute_boundary_found = True
                print(f"      🎯 FOUND: compute_next_boundary entity!")
        
        # Check the code context to see if implementations were matched
        code_context = result.get("code_context", [])
        compute_boundary_code_found = False
        
        print(f"\n📊 Code Context Analysis ({len(code_context)} implementations):")
        for i, code in enumerate(code_context, 1):
            entity_name = code.get('entity_name', 'unknown')
            relevance_score = code.get('relevance_score', 0)
            source_length = len(code.get('source_code', ''))
            
            print(f"   {i}. {entity_name} (score: {relevance_score:.2f}, code: {source_length} chars)")
            
            if 'compute_next_boundary' in entity_name.lower():
                compute_boundary_code_found = True
                print(f"      🎯 FOUND: compute_next_boundary implementation!")
                
                # Show a preview of the code
                source_code = code.get('source_code', '')
                preview = source_code[:200] + "..." if len(source_code) > 200 else source_code
                print(f"      📄 Code preview: {preview}")
        
        # Check if LLM-friendly package was generated and contains the function
        if "llm_friendly_package" in result:
            package = result["llm_friendly_package"]
            print(f"\n📦 LLM Package Analysis ({len(package)} characters)")
            
            if "compute_next_boundary" in package.lower():
                print("✅ SUCCESS: Package contains 'compute_next_boundary'!")
                
                # Count occurrences
                count = package.lower().count("compute_next_boundary")
                print(f"📊 Function appears {count} times in the package")
                
                # Check if it appears in KEY IMPLEMENTATIONS section
                if "## KEY IMPLEMENTATIONS" in package and "compute_next_boundary" in package[package.find("## KEY IMPLEMENTATIONS"):]:
                    print("🎉 CRITICAL SUCCESS: compute_next_boundary appears in KEY IMPLEMENTATIONS section!")
                    return True
                else:
                    print("⚠️  Function found but NOT in KEY IMPLEMENTATIONS section")
                    return False
            else:
                print("❌ FAILURE: Package does NOT contain 'compute_next_boundary'")
                return False
        else:
            print("❌ No LLM-friendly package generated!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the enhanced matching test."""
    print("🚀 Testing Enhanced Code Matching Logic")
    print("=" * 60)
    
    success = test_enhanced_matching()
    
    if success:
        print("\n🎉 TEST PASSED: Enhanced matching logic is working!")
        print("   The compute_next_boundary function is now being properly matched and included in KEY IMPLEMENTATIONS.")
    else:
        print("\n❌ TEST FAILED: Enhanced matching logic still has issues.")
        print("   The compute_next_boundary function is still not being matched properly.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
