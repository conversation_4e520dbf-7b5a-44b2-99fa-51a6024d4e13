#!/usr/bin/env python3
"""
Test the conversation-level optimization for the "new/" prefix
"""

import sys
import os
import time
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

class MockIO:
    """Mock IO for testing."""
    def tool_output(self, msg):
        print(f"🔧 {msg}")
    
    def tool_warning(self, msg):
        print(f"⚠️  {msg}")

class MockCoder:
    """Mock coder to test the process_direct_ir_context method."""
    
    def __init__(self):
        self.io = MockIO()
        self.cur_messages = []
        # Start without any context injected
        # self.ir_context_injected = False  # Don't set this initially
    
    def _get_project_path_for_context(self):
        return r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
    
    def _extract_focus_entities_from_query(self, query):
        # Simple extraction for testing
        words = query.lower().split()
        return [w for w in words if len(w) > 3][:5]

def test_conversation_optimization():
    """Test the conversation-level optimization."""
    
    print("🧪 Testing Conversation-Level 'new/' Prefix Optimization")
    print("=" * 70)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        # Import the process_direct_ir_context method
        from aider.coders.base_coder import Coder
        
        # Create a mock coder
        coder = MockCoder()
        
        # Import the actual method and bind it to our mock coder
        import types
        coder.process_direct_ir_context = types.MethodType(Coder.process_direct_ir_context, coder)
        
        print(f"📁 Testing with project: {project_path}")
        
        # Test sequence simulating a real conversation
        test_scenarios = [
            {
                "query": "How does the compute_next_boundary function work?",
                "description": "First question (should generate context)",
                "expected_result": True,
                "expected_time": "> 2s"
            },
            {
                "query": "What parameters does it take?",
                "description": "Follow-up without 'new/' (should skip IR generation)",
                "expected_result": False,
                "expected_time": "< 0.1s"
            },
            {
                "query": "new/how does the close_position_based_on_conditions function work?",
                "description": "Follow-up with 'new/' (should generate fresh context)",
                "expected_result": True,
                "expected_time": "> 2s"
            }
        ]
        
        results = []
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📝 Test {i}: {scenario['description']}")
            print(f"   Query: '{scenario['query']}'")
            print(f"   Expected result: {scenario['expected_result']}")
            print(f"   Expected time: {scenario['expected_time']}")
            
            start_time = time.time()
            
            # Call the process_direct_ir_context method
            result = coder.process_direct_ir_context(scenario['query'])
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"   Actual result: {result}")
            print(f"   Actual time: {processing_time:.3f}s")
            
            # Check if the result matches expectations
            if result == scenario['expected_result']:
                if scenario['expected_result']:
                    # Should have generated context (slow)
                    if processing_time > 2.0:
                        print(f"✅ Test {i}: PASS - Generated context as expected")
                        results.append(True)
                    else:
                        print(f"❌ Test {i}: FAIL - Expected slow generation but was fast")
                        results.append(False)
                else:
                    # Should have skipped generation (fast)
                    if processing_time < 0.1:
                        print(f"✅ Test {i}: PASS - Skipped generation as expected")
                        results.append(True)
                    else:
                        print(f"❌ Test {i}: FAIL - Expected fast skip but was slow")
                        results.append(False)
            else:
                print(f"❌ Test {i}: FAIL - Result mismatch (expected {scenario['expected_result']}, got {result})")
                results.append(False)
        
        # Evaluate results
        print(f"\n📊 Conversation Optimization Test Results:")
        print(f"=" * 60)
        
        passed_tests = sum(results)
        total_tests = len(results)
        
        for i, passed in enumerate(results, 1):
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"   Test {i}: {status}")
        
        print(f"\n🎯 Summary: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print(f"\n🎉 CONVERSATION OPTIMIZATION SUCCESS!")
            print(f"   ✅ First questions generate fresh context")
            print(f"   ✅ Follow-ups skip generation (efficient)")
            print(f"   ✅ 'new/' prefix forces fresh context")
        else:
            print(f"\n❌ CONVERSATION OPTIMIZATION INCOMPLETE")
            print(f"   The optimization logic needs refinement")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the conversation optimization test."""
    print("🚀 Testing Conversation-Level 'new/' Prefix Optimization")
    print("=" * 70)
    
    success = test_conversation_optimization()
    
    if success:
        print("\n🎉 CONVERSATION OPTIMIZATION TEST PASSED!")
        print("The 'new/' prefix system is working at the conversation level.")
    else:
        print("\n❌ CONVERSATION OPTIMIZATION TEST FAILED!")
        print("The conversation-level optimization needs additional work.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
