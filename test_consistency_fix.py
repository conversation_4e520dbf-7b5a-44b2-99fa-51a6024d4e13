#!/usr/bin/env python3
"""
Test the consistency fix between CRITICAL ENTITIES and KEY IMPLEMENTATIONS sections
"""

import sys
import os
import time
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_consistency_fix():
    """Test that CRITICAL ENTITIES and KEY IMPLEMENTATIONS sections are now consistent."""
    
    print("🧪 Testing Consistency Fix Between CRITICAL ENTITIES and KEY IMPLEMENTATIONS")
    print("=" * 80)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        from aider.context_request import ContextRequestHandler, IRContextRequest
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test with the same query that showed inconsistency
        test_query = "how does the position quantity calculator function work?"
        
        print(f"📝 Testing query: '{test_query}'")
        
        # Create IR context request
        request = IRContextRequest(
            user_query=test_query,
            task_description=f"Analyze and provide context for: {test_query}",
            task_type="general_analysis",
            focus_entities=["position", "quantity", "calculator", "function"],
            max_tokens=2000,
            llm_friendly=True,
            max_entities=8,
            include_ir_slices=True,
            include_code_context=True
        )
        
        print(f"🔄 Processing IR context request with consistency fix...")
        
        start_time = time.time()
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if "error" in result:
            print(f"❌ IR context request failed: {result['error']}")
            return False
        
        print(f"✅ IR context request processed successfully in {processing_time:.2f}s!")
        
        # Check if LLM-friendly package was generated
        if "llm_friendly_package" in result:
            package = result["llm_friendly_package"]
            print(f"\n📦 LLM Package Analysis ({len(package)} characters)")
            
            # Extract CRITICAL ENTITIES section
            critical_entities_section = ""
            key_implementations_section = ""
            
            if "## CRITICAL ENTITIES" in package:
                start_idx = package.find("## CRITICAL ENTITIES")
                end_idx = package.find("## KEY IMPLEMENTATIONS")
                if end_idx != -1:
                    critical_entities_section = package[start_idx:end_idx]
                else:
                    critical_entities_section = package[start_idx:]
            
            if "## KEY IMPLEMENTATIONS" in package:
                start_idx = package.find("## KEY IMPLEMENTATIONS")
                end_idx = package.find("## ANALYSIS INSTRUCTIONS")
                if end_idx != -1:
                    key_implementations_section = package[start_idx:end_idx]
                else:
                    key_implementations_section = package[start_idx:]
            
            print(f"\n🔍 Analyzing Section Consistency...")
            
            # Extract entity names from CRITICAL ENTITIES section
            critical_entity_names = []
            if critical_entities_section:
                lines = critical_entities_section.split('\n')
                for line in lines:
                    if line.strip().startswith('### '):
                        # Extract entity name from header like "### 1. _calculate_position_quantity (function)"
                        parts = line.split('.')
                        if len(parts) > 1:
                            entity_part = parts[1].strip()
                            # Remove type annotation in parentheses
                            if '(' in entity_part:
                                entity_name = entity_part.split('(')[0].strip()
                                critical_entity_names.append(entity_name)
            
            # Extract entity names from KEY IMPLEMENTATIONS section
            key_implementation_names = []
            if key_implementations_section:
                lines = key_implementations_section.split('\n')
                for line in lines:
                    if line.strip().startswith('### '):
                        # Extract entity name from header like "### 1. _calculate_position_quantity"
                        parts = line.split('.')
                        if len(parts) > 1:
                            entity_name = parts[1].strip()
                            key_implementation_names.append(entity_name)
            
            print(f"📊 Section Analysis Results:")
            print(f"   CRITICAL ENTITIES: {len(critical_entity_names)} entities")
            for i, name in enumerate(critical_entity_names, 1):
                print(f"      {i}. {name}")
            
            print(f"   KEY IMPLEMENTATIONS: {len(key_implementation_names)} entities")
            for i, name in enumerate(key_implementation_names, 1):
                print(f"      {i}. {name}")
            
            # Check for consistency
            critical_set = set(critical_entity_names)
            implementation_set = set(key_implementation_names)
            
            if critical_set == implementation_set:
                print(f"\n🎉 CONSISTENCY SUCCESS!")
                print(f"   ✅ CRITICAL ENTITIES and KEY IMPLEMENTATIONS sections match perfectly")
                print(f"   ✅ Both sections show the same {len(critical_set)} entities")
                
                # Check if we found the expected function
                if "_calculate_position_quantity" in critical_set:
                    print(f"   🎯 SUCCESS: Found the expected '_calculate_position_quantity' function")
                    
                    # Check if the function implementation is complete
                    if "def _calculate_position_quantity" in package:
                        if "# ... (implementation continues)" in package:
                            print(f"   ⚠️  WARNING: Function implementation is truncated")
                            return False
                        else:
                            print(f"   ✅ COMPLETE: Function implementation is NOT truncated")
                            return True
                    else:
                        print(f"   ❌ ERROR: Function definition not found in package")
                        return False
                else:
                    print(f"   ⚠️  WARNING: Expected '_calculate_position_quantity' function not found")
                    print(f"   📋 Found entities: {list(critical_set)}")
                    return False
            else:
                print(f"\n❌ CONSISTENCY FAILURE!")
                print(f"   ❌ CRITICAL ENTITIES and KEY IMPLEMENTATIONS sections do NOT match")
                
                only_in_critical = critical_set - implementation_set
                only_in_implementations = implementation_set - critical_set
                
                if only_in_critical:
                    print(f"   📋 Only in CRITICAL ENTITIES: {list(only_in_critical)}")
                if only_in_implementations:
                    print(f"   📋 Only in KEY IMPLEMENTATIONS: {list(only_in_implementations)}")
                
                return False
        else:
            print(f"❌ No LLM-friendly package generated!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the consistency fix test."""
    print("🚀 Testing Consistency Fix for IR Context Packages")
    print("=" * 80)
    
    success = test_consistency_fix()
    
    if success:
        print("\n🎉 CONSISTENCY FIX TEST PASSED!")
        print("The CRITICAL ENTITIES and KEY IMPLEMENTATIONS sections are now perfectly aligned.")
        print("Users will see consistent information in both sections.")
    else:
        print("\n❌ CONSISTENCY FIX TEST FAILED!")
        print("There are still inconsistencies between the sections.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
