#!/usr/bin/env python3
"""
Test the compute_next_boundary relevance matching fix
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_compute_next_boundary_fix():
    """Test if the relevance matching fix works for compute_next_boundary."""
    
    print("🧪 Testing compute_next_boundary relevance matching fix")
    print("=" * 60)
    
    try:
        from aider.context_request import ContextRequestH<PERSON><PERSON>, IRContextRequest
        
        # Use the live_backtest_dashboard project path
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        
        if not os.path.exists(project_path):
            print(f"❌ Project path not found: {project_path}")
            return False
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test with the specific query that was failing
        test_query = "How does the compute_next_boundary function work?"
        
        print(f"📝 Testing query: {test_query}")
        
        # Create IR context request
        request = IRContextRequest(
            user_query=test_query,
            task_description=f"Analyze and provide context for: {test_query}",
            task_type="general_analysis",
            focus_entities=["compute_next_boundary", "function", "work"],
            max_tokens=2000,
            llm_friendly=True,
            max_entities=8
        )
        
        print("🔄 Processing IR context request...")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        if "error" in result:
            print(f"❌ IR context request failed: {result['error']}")
            return False
        
        print("✅ IR context request processed successfully!")
        
        # Check if LLM-friendly package was generated
        if "llm_friendly_package" in result:
            package = result["llm_friendly_package"]
            print(f"📦 Generated LLM package ({len(package)} characters)")
            
            # Check if the package contains the specific function we're looking for
            if "compute_next_boundary" in package.lower():
                print("🎉 SUCCESS: Package contains 'compute_next_boundary' - exact match found!")
                
                # Count how many times it appears
                count = package.lower().count("compute_next_boundary")
                print(f"📊 Function appears {count} times in the package")
                
                # Show a preview of the relevant section
                lines = package.split('\n')
                relevant_lines = []
                for i, line in enumerate(lines):
                    if 'compute_next_boundary' in line.lower():
                        # Include some context around the match
                        start = max(0, i-2)
                        end = min(len(lines), i+3)
                        relevant_lines.extend(lines[start:end])
                        relevant_lines.append("---")
                
                if relevant_lines:
                    print("\n📄 Relevant sections containing compute_next_boundary:")
                    print("\n".join(relevant_lines[:20]))  # Show first 20 lines
                
                return True
            else:
                print("❌ FAILURE: Package does NOT contain 'compute_next_boundary' - exact match still missing!")
                
                # Show what functions are included instead
                print("\n📄 Functions included in package:")
                lines = package.split('\n')
                function_lines = [line for line in lines if 'function)' in line or 'async_function)' in line]
                for line in function_lines[:10]:  # Show first 10 functions
                    print(f"   - {line.strip()}")
                
                return False
        else:
            print("❌ No LLM-friendly package generated!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the compute_next_boundary fix test."""
    print("🚀 Testing IR Context System Relevance Matching Fix")
    print("=" * 60)
    
    success = test_compute_next_boundary_fix()
    
    if success:
        print("\n🎉 TEST PASSED: Relevance matching fix is working!")
        print("   The compute_next_boundary function is now being properly matched and included.")
    else:
        print("\n❌ TEST FAILED: Relevance matching issue still exists.")
        print("   The compute_next_boundary function is still not being matched properly.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
