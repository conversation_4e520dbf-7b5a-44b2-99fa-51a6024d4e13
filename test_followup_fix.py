#!/usr/bin/env python3
"""
Test the follow-up question fix for IR context injection
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_followup_fix():
    """Test that follow-up questions get fresh IR context."""
    
    print("🧪 Testing Follow-up Question IR Context Fix")
    print("=" * 60)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        from aider.context_request import ContextRequestHand<PERSON>, IRContextRequest
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test sequence: First question → Follow-up question
        test_queries = [
            "How does the compute_next_boundary function work?",
            "how does the close_position_based_on_conditions function work?"
        ]
        
        results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 Testing Query {i}: {query}")
            
            # Create IR context request
            request = IRContextRequest(
                user_query=query,
                task_description=f"Analyze and provide context for: {query}",
                task_type="general_analysis",
                focus_entities=[],  # Let the system extract them
                max_tokens=2000,
                llm_friendly=True,
                max_entities=8,
                include_ir_slices=True,
                include_code_context=True
            )
            
            print(f"🔄 Processing IR context request {i}...")
            
            # Process the request
            result = handler.process_ir_context_request(request)
            
            if "error" in result:
                print(f"❌ Query {i} failed: {result['error']}")
                results.append(False)
                continue
            
            print(f"✅ Query {i} processed successfully!")
            
            # Check if LLM-friendly package was generated
            if "llm_friendly_package" in result:
                package = result["llm_friendly_package"]
                print(f"📦 Package {i} generated: {len(package)} characters")
                
                # Check for the specific function in each query
                if i == 1:
                    # First query: compute_next_boundary
                    if "compute_next_boundary" in package.lower():
                        print(f"✅ Query {i}: compute_next_boundary found in package")
                        results.append(True)
                    else:
                        print(f"❌ Query {i}: compute_next_boundary NOT found in package")
                        results.append(False)
                elif i == 2:
                    # Second query: close_position_based_on_conditions
                    if "close_position_based_on_conditions" in package.lower():
                        print(f"✅ Query {i}: close_position_based_on_conditions found in package")
                        
                        # Check if it's in KEY IMPLEMENTATIONS (not truncated)
                        if "## KEY IMPLEMENTATIONS" in package:
                            key_impl_section = package[package.find("## KEY IMPLEMENTATIONS"):]
                            if "close_position_based_on_conditions" in key_impl_section.lower():
                                if "# ... (implementation continues)" in key_impl_section:
                                    print(f"⚠️  Query {i}: Function found but truncated")
                                    results.append(False)
                                else:
                                    print(f"🎉 Query {i}: Complete function implementation found!")
                                    results.append(True)
                            else:
                                print(f"❌ Query {i}: Function not in KEY IMPLEMENTATIONS")
                                results.append(False)
                        else:
                            print(f"❌ Query {i}: No KEY IMPLEMENTATIONS section")
                            results.append(False)
                    else:
                        print(f"❌ Query {i}: close_position_based_on_conditions NOT found in package")
                        results.append(False)
            else:
                print(f"❌ Query {i}: No LLM-friendly package generated")
                results.append(False)
        
        # Evaluate results
        print(f"\n📊 Test Results Summary:")
        print(f"   Query 1 (compute_next_boundary): {'✅ PASS' if results[0] else '❌ FAIL'}")
        print(f"   Query 2 (close_position_based_on_conditions): {'✅ PASS' if results[1] else '❌ FAIL'}")
        
        overall_success = all(results)
        
        if overall_success:
            print(f"\n🎉 FOLLOW-UP FIX SUCCESS!")
            print(f"   Both queries generated correct context packages")
            print(f"   The ir_context_injected flag fix is working!")
        else:
            print(f"\n❌ FOLLOW-UP FIX INCOMPLETE")
            print(f"   Some queries still have issues")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the follow-up fix test."""
    print("🚀 Testing Follow-up Question IR Context Fix")
    print("=" * 60)
    
    success = test_followup_fix()
    
    if success:
        print("\n🎉 TEST PASSED: Follow-up question fix is working!")
        print("   Users can now ask multiple questions in sequence and get proper context for each.")
    else:
        print("\n❌ TEST FAILED: Follow-up question fix still has issues.")
        print("   Additional debugging may be needed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
