#!/usr/bin/env python3
"""
Test the fix for close_position_based_on_conditions function truncation
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to the Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

def test_close_position_fix():
    """Test the fix for close_position_based_on_conditions function."""
    
    print("🧪 Testing close_position_based_on_conditions fix")
    print("=" * 60)
    
    try:
        # Change to the live_backtest_dashboard directory
        project_path = r"C:\Users\<USER>\Documents\____live_backtest_dashboard_____"
        os.chdir(project_path)
        
        from aider.context_request import ContextR<PERSON>quest<PERSON><PERSON><PERSON>, IRContextRequest
        
        print(f"📁 Testing with project: {project_path}")
        
        # Create context request handler
        handler = ContextRequestHandler(project_path)
        
        # Test with the specific query about close_position_based_on_conditions
        test_query = "how does the close_position_based_on_conditions function work?"
        
        print(f"📝 Testing query: {test_query}")
        
        # Create IR context request
        request = IRContextRequest(
            user_query=test_query,
            task_description=f"Analyze and provide context for: {test_query}",
            task_type="general_analysis",
            focus_entities=["close_position_based_on_conditions", "function", "work"],
            max_tokens=2000,
            llm_friendly=True,
            max_entities=8,
            include_ir_slices=True,
            include_code_context=True
        )
        
        print("🔄 Processing IR context request with enhanced limits...")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        if "error" in result:
            print(f"❌ IR context request failed: {result['error']}")
            return False
        
        print("✅ IR context request processed successfully!")
        
        # Check if LLM-friendly package was generated and contains the function
        if "llm_friendly_package" in result:
            package = result["llm_friendly_package"]
            print(f"\n📦 LLM Package Analysis ({len(package)} characters)")
            
            if "close_position_based_on_conditions" in package.lower():
                print("✅ SUCCESS: Package contains 'close_position_based_on_conditions'!")
                
                # Count occurrences
                count = package.lower().count("close_position_based_on_conditions")
                print(f"📊 Function appears {count} times in the package")
                
                # Check if it appears in KEY IMPLEMENTATIONS section
                if "## KEY IMPLEMENTATIONS" in package:
                    key_impl_section = package[package.find("## KEY IMPLEMENTATIONS"):]
                    if "close_position_based_on_conditions" in key_impl_section.lower():
                        print("🎉 CRITICAL SUCCESS: close_position_based_on_conditions appears in KEY IMPLEMENTATIONS section!")
                        
                        # Check if the implementation is complete (not truncated)
                        if "# ... (implementation continues)" in key_impl_section:
                            print("⚠️  WARNING: Function implementation is still truncated!")
                            return False
                        else:
                            print("✅ COMPLETE: Function implementation is NOT truncated!")
                            
                        # Check how much of the function is shown
                        func_start = key_impl_section.find("close_position_based_on_conditions")
                        if func_start != -1:
                            func_section = key_impl_section[func_start:func_start+2000]
                            lines = func_section.count('\n')
                            print(f"📏 Function shows approximately {lines} lines of code")
                            
                        return True
                    else:
                        print("❌ Function found but NOT in KEY IMPLEMENTATIONS section")
                        return False
                else:
                    print("❌ No KEY IMPLEMENTATIONS section found")
                    return False
            else:
                print("❌ FAILURE: Package does NOT contain 'close_position_based_on_conditions'")
                return False
        else:
            print("❌ No LLM-friendly package generated!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the close_position fix test."""
    print("🚀 Testing close_position_based_on_conditions Fix")
    print("=" * 60)
    
    success = test_close_position_fix()
    
    if success:
        print("\n🎉 TEST PASSED: close_position_based_on_conditions fix is working!")
        print("   The function is now being properly included with complete implementation.")
    else:
        print("\n❌ TEST FAILED: close_position_based_on_conditions fix still has issues.")
        print("   The function is still being truncated or not included properly.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
